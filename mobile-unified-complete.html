<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#d50000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Loacker">
    
    <title>Loacker - دليل المتاجر الموحد</title>
    
    <!-- الخطوط العربية -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Leaflet للخرائط -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    
    <style>
        /**
         * ملف CSS موحد ومنظم لواجهة الهاتف المحمول - Loacker
         * جميع التنسيقات والأنماط في ملف واحد
         */

        /* ===== 1. المتغيرات الأساسية ===== */
        :root {
            /* ألوان Loacker */
            --loacker-red: #d50000;
            --loacker-red-light: #ff5131;
            --loacker-red-dark: #9b0000;
            
            /* أبعاد الواجهة */
            --header-height: 60px;
            --footer-height: 60px;
            --content-padding: 15px;
            --card-border-radius: 12px;
            --button-border-radius: 8px;
            
            /* الظلال */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 6px 16px rgba(0, 0, 0, 0.2);
            
            /* الانتقالات */
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;
            
            /* متغيرات الأجهزة */
            --device-width: 100vw;
            --device-height: 100vh;
            --device-pixel-ratio: 1;
            --safe-area-inset-top: 0px;
            --safe-area-inset-bottom: 0px;
            --font-size-multiplier: 1;
            --device-font-family: "Tajawal", "Cairo", sans-serif;
        }

        /* ===== 2. إعدادات أساسية ===== */
        * {
            box-sizing: border-box;
        }
        
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow-x: hidden;
            font-family: var(--device-font-family);
        }

        /* ===== 3. كشف الأجهزة والتوافق ===== */
        body.mobile-device {
            font-family: var(--device-font-family);
            font-size: calc(14px * var(--font-size-multiplier));
            padding-top: var(--safe-area-inset-top);
            padding-bottom: var(--safe-area-inset-bottom);
            background-color: #f8f9fa;
            margin: 0;
            overflow-x: hidden;
        }

        body.mobile-device.small-phone {
            font-size: calc(13px * var(--font-size-multiplier));
        }

        body.mobile-device.large-phone {
            font-size: calc(15px * var(--font-size-multiplier));
        }

        body.mobile-device.iphone-device {
            --ios-blue: #007aff;
            --ios-red: #ff3b30;
            --ios-green: #34c759;
        }

        body.mobile-device.xiaomi-device {
            --miui-blue: #0e87fa;
            --miui-orange: #ff6700;
        }

        body.mobile-device.samsung-device {
            --samsung-blue: #1428a0;
            --samsung-light-blue: #75b7ff;
        }

        /* ===== 4. الهيكل الأساسي للواجهة ===== */
        .desktop-view {
            display: block;
        }
        
        .mobile-view {
            display: none;
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            background-color: #f8f9fa !important;
            color: #333 !important;
            font-family: 'Tajawal', sans-serif;
            height: 100vh;
            position: relative;
            flex-direction: column;
        }

        /* عرض واجهة الهاتف على الأجهزة المحمولة */
        @media (max-width: 768px) {
            .desktop-view {
                display: none !important;
            }
            
            .mobile-view {
                display: flex !important;
            }
        }

        body.mobile-device .mobile-view {
            display: flex;
            flex-direction: column;
        }

        body.mobile-device .desktop-view {
            display: none !important;
        }

        /* ===== 5. أنماط الرأس والتذييل ===== */
        .mobile-header {
            background-color: white !important;
            color: #333 !important;
            box-shadow: var(--shadow-sm) !important;
            height: var(--header-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--content-padding);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid #eee;
        }

        .mobile-header .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--loacker-red);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .mobile-header .menu-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #333;
            padding: 0.5rem;
            border-radius: var(--button-border-radius);
            transition: var(--transition-fast);
            cursor: pointer;
        }

        .mobile-header .menu-btn:hover {
            background-color: #f5f5f5;
        }

        /* محتوى الصفحة */
        .mobile-content {
            flex: 1;
            background-color: #f8f9fa !important;
            color: #333 !important;
            padding: var(--content-padding);
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* تذييل الصفحة مع التبويبات */
        .mobile-tabs {
            background-color: white !important;
            border-top: 1px solid #eee !important;
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1) !important;
            height: var(--footer-height);
            display: flex;
            align-items: center;
            position: sticky;
            bottom: 0;
            z-index: 1000;
        }

        /* ===== 6. أنماط الأزرار والتفاعل ===== */
        .mobile-tab-btn {
            background-color: white !important;
            color: #666 !important;
            border: none;
            border-radius: 0;
            padding: 0.75rem 0;
            font-size: 0.85rem;
            transition: var(--transition-normal);
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .mobile-tab-btn i {
            font-size: 1.25rem;
            transition: var(--transition-normal);
        }

        .mobile-tab-btn:active {
            background-color: #f5f5f5 !important;
            transform: scale(0.95);
        }

        .mobile-tab-btn.active {
            color: var(--loacker-red) !important;
        }

        .mobile-tab-btn.active i {
            transform: scale(1.1);
        }

        /* أزرار عامة */
        .mobile-btn {
            padding: 0.75rem 1.5rem;
            border-radius: var(--button-border-radius);
            border: none;
            font-weight: 500;
            transition: var(--transition-normal);
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            min-height: 44px;
        }

        .mobile-btn-primary {
            background-color: var(--loacker-red);
            color: white;
        }

        .mobile-btn-primary:hover {
            background-color: var(--loacker-red-dark);
        }

        .mobile-btn:active {
            transform: scale(0.95);
        }

        /* ===== 7. أنماط المحتوى والبطاقات ===== */
        .mobile-store-card {
            background: white;
            border-radius: var(--card-border-radius);
            box-shadow: var(--shadow-sm);
            margin-bottom: 1rem;
            padding: 1rem;
            transition: var(--transition-normal);
            border: 1px solid #eee;
        }

        .mobile-store-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .mobile-store-card .store-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .mobile-store-card .store-address {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .mobile-store-card .store-phone {
            color: var(--loacker-red);
            font-weight: 500;
            text-decoration: none;
        }

        .mobile-store-card .store-phone a {
            color: var(--loacker-red);
            text-decoration: none;
        }

        /* ===== 8. أنماط الخريطة ===== */
        .mobile-map-container {
            height: 300px;
            border-radius: var(--card-border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1rem;
            position: relative;
        }

        .mobile-map {
            width: 100%;
            height: 100%;
        }

        .mobile-map-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .mobile-map-control-btn {
            background: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-sm);
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .mobile-map-control-btn:hover {
            box-shadow: var(--shadow-md);
        }

        /* ===== 9. أنماط الإحصائيات ===== */
        .mobile-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .mobile-stat-card {
            background: white;
            padding: 1rem;
            border-radius: var(--card-border-radius);
            text-align: center;
            box-shadow: var(--shadow-sm);
            border: 1px solid #eee;
        }

        .mobile-stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--loacker-red);
            display: block;
        }

        .mobile-stat-label {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.25rem;
        }

        /* ===== 10. أنماط المشاركة ===== */
        .mobile-share-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .mobile-share-btn {
            flex: 1;
            padding: 0.75rem;
            border: none;
            border-radius: var(--button-border-radius);
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-fast);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .mobile-share-whatsapp {
            background-color: #25d366;
        }

        .mobile-share-whatsapp:hover {
            background-color: #1da851;
        }

        .mobile-share-copy {
            background-color: #6c757d;
        }

        .mobile-share-copy:hover {
            background-color: #545b62;
        }

        /* ===== 11. أنماط الإشعارات ===== */
        .mobile-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: #666;
        }

        .mobile-loading-spinner {
            width: 2rem;
            height: 2rem;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--loacker-red);
            border-radius: 50%;
            animation: mobile-spin 1s linear infinite;
            margin-right: 0.75rem;
        }

        @keyframes mobile-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .mobile-toast {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-size: 0.9rem;
            z-index: 3000;
            opacity: 0;
            transition: opacity var(--transition-normal);
        }

        .mobile-toast.show {
            opacity: 1;
        }

        /* ===== 12. أنماط التبويبات ===== */
        .mobile-tab-content {
            display: none;
        }

        .mobile-tab-content.active {
            display: block;
        }

        /* ===== 13. أنماط النماذج ===== */
        .mobile-form-group {
            margin-bottom: 1rem;
        }

        .mobile-form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .mobile-form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: var(--button-border-radius);
            font-size: 1rem;
            transition: var(--transition-fast);
            background-color: white;
        }

        .mobile-form-input:focus {
            outline: none;
            border-color: var(--loacker-red);
            box-shadow: 0 0 0 3px rgba(213, 0, 0, 0.1);
        }

        .mobile-form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 1rem;
            padding-right: 2.5rem;
        }

        /* ===== 14. استعلامات الوسائط ===== */
        @media (max-width: 319px) {
            :root {
                --content-padding: 10px;
                --font-size-multiplier: 0.9;
            }
            
            .mobile-header {
                padding: 0 10px;
            }
            
            .mobile-tab-btn {
                font-size: 0.75rem;
                padding: 0.5rem 0;
            }
            
            .mobile-tab-btn i {
                font-size: 1rem;
            }
        }

        @media (min-width: 481px) and (max-width: 768px) {
            :root {
                --content-padding: 20px;
                --font-size-multiplier: 1.1;
            }
            
            .mobile-store-card {
                padding: 1.25rem;
            }
            
            .mobile-map-container {
                height: 350px;
            }
        }

        @media (orientation: landscape) and (max-height: 500px) {
            .mobile-header {
                height: 50px;
            }
            
            .mobile-tabs {
                height: 50px;
            }
            
            .mobile-content {
                padding: 10px;
            }
            
            .mobile-map-container {
                height: 200px;
            }
        }

        /* ===== 15. إصلاحات خاصة بالأجهزة ===== */
        .mobile-view.iphone-view {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", sans-serif;
        }

        .mobile-view.iphone-view button,
        .mobile-view.iphone-view .mobile-btn {
            border-radius: 10px;
        }

        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .mobile-view {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }

        @supports (padding: max(0px)) {
            .mobile-view {
                padding-top: max(var(--safe-area-inset-top), 0px);
                padding-bottom: max(var(--safe-area-inset-bottom), 0px);
            }
        }

        .mobile-content {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        .mobile-form-input,
        .mobile-form-select {
            font-size: 16px; /* منع التكبير التلقائي في iOS */
        }

        .mobile-view * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        .mobile-form-input {
            -webkit-user-select: text;
            user-select: text;
        }

        /* إصلاحات للأداء */
        .mobile-view {
            will-change: transform;
            transform: translateZ(0);
        }

        .mobile-store-card,
        .mobile-btn,
        .mobile-tab-btn {
            will-change: transform;
            transform: translateZ(0);
        }

        /* ===== 16. أنماط إضافية ===== */
        .mobile-no-results {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .mobile-search-bar {
            position: relative;
            margin-bottom: 1rem;
        }

        .mobile-search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
            background-color: white;
            transition: var(--transition-fast);
        }

        .mobile-search-input:focus {
            outline: none;
            border-color: var(--loacker-red);
            box-shadow: 0 0 0 3px rgba(213, 0, 0, 0.1);
        }

        .mobile-search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 1.1rem;
        }

        .mobile-filters {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
            -webkit-overflow-scrolling: touch;
        }

        .mobile-filter-btn {
            background-color: white;
            border: 2px solid #ddd;
            color: #666;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            white-space: nowrap;
            transition: var(--transition-fast);
            cursor: pointer;
        }

        .mobile-filter-btn.active {
            background-color: var(--loacker-red);
            border-color: var(--loacker-red);
            color: white;
        }

        .mobile-filter-btn:hover {
            border-color: var(--loacker-red);
        }

        .mobile-stores-list {
            padding: 0;
            margin: 0;
        }

        .mobile-store-item {
            list-style: none;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body class="mobile-device">
    
    <!-- واجهة سطح المكتب -->
    <div class="desktop-view">
        <div style="padding: 2rem; text-align: center; font-family: 'Tajawal', sans-serif;">
            <h1 style="color: #d50000;">مرحباً بك في Loacker</h1>
            <p>يرجى استخدام هاتفك المحمول للوصول إلى التطبيق</p>
            <i class="fas fa-mobile-alt" style="font-size: 4rem; color: #d50000; margin: 2rem 0;"></i>
        </div>
    </div>

    <!-- واجهة الهاتف المحمول -->
    <div class="mobile-view">

        <!-- رأس الصفحة -->
        <header class="mobile-header">
            <div class="logo">
                <i class="fas fa-store"></i>
                Loacker
            </div>
            <button class="menu-btn" id="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </header>

        <!-- محتوى الصفحة -->
        <main class="mobile-content">

            <!-- شريط البحث -->
            <div id="mobile-search-container">
                <div class="mobile-search-bar">
                    <i class="fas fa-search mobile-search-icon"></i>
                    <input type="text" id="mobile-search-input" class="mobile-search-input" placeholder="البحث في المتاجر...">
                </div>
            </div>

            <!-- الفلاتر -->
            <div id="mobile-filters-container">
                <div class="mobile-filters">
                    <button class="mobile-filter-btn active" data-filter="all">الكل</button>
                    <button class="mobile-filter-btn" data-filter="nearby">القريبة</button>
                    <button class="mobile-filter-btn" data-filter="favorites">المفضلة</button>
                </div>
            </div>

            <!-- محتوى التبويبات -->
            <div class="mobile-tabs-content">

                <!-- تبويب الخريطة -->
                <div id="mobile-map-tab" class="mobile-tab-content active">
                    <div class="mobile-stats">
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-total-stores-count">0</span>
                            <span class="mobile-stat-label">إجمالي المتاجر</span>
                        </div>
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-regions-count">0</span>
                            <span class="mobile-stat-label">المناطق</span>
                        </div>
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-avg-stores-per-region">0</span>
                            <span class="mobile-stat-label">متوسط/منطقة</span>
                        </div>
                    </div>

                    <div class="mobile-map-container">
                        <div id="mobile-map" class="mobile-map"></div>
                    </div>
                </div>

                <!-- تبويب القائمة -->
                <div id="mobile-list-tab" class="mobile-tab-content">
                    <div id="mobile-lists-container" class="mobile-filters">
                        <button class="mobile-filter-btn active" data-filter="all">جميع المتاجر</button>
                    </div>
                    <div id="mobile-stores-container" class="mobile-stores-list">
                        <!-- سيتم ملء المتاجر هنا ديناميكياً -->
                    </div>
                </div>

                <!-- تبويب الإحصائيات -->
                <div id="mobile-stats-tab" class="mobile-tab-content">
                    <div class="mobile-stats">
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-total-stores-count-2">0</span>
                            <span class="mobile-stat-label">إجمالي المتاجر</span>
                        </div>
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-regions-count-2">0</span>
                            <span class="mobile-stat-label">عدد المناطق</span>
                        </div>
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-avg-stores-per-region-2">0</span>
                            <span class="mobile-stat-label">متوسط المتاجر لكل منطقة</span>
                        </div>
                    </div>

                    <div class="mobile-store-card">
                        <h6><i class="fas fa-chart-bar"></i> إحصائيات مفصلة</h6>
                        <p>ستتوفر الإحصائيات المفصلة قريباً...</p>
                        <div class="mobile-form-group">
                            <button class="mobile-btn mobile-btn-primary" style="width: 100%;">
                                <i class="fas fa-download"></i> تصدير البيانات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- تبويب الإعدادات -->
                <div id="mobile-settings-tab" class="mobile-tab-content">
                    <div class="mobile-store-card">
                        <h6><i class="fas fa-cog"></i> الإعدادات</h6>

                        <div class="mobile-form-group">
                            <label class="mobile-form-label">اللغة</label>
                            <select class="mobile-form-input mobile-form-select">
                                <option value="ar">العربية</option>
                                <option value="en">English</option>
                            </select>
                        </div>

                        <div class="mobile-form-group">
                            <label class="mobile-form-label">المظهر</label>
                            <select class="mobile-form-input mobile-form-select">
                                <option value="light">فاتح</option>
                                <option value="dark">داكن</option>
                                <option value="auto">تلقائي</option>
                            </select>
                        </div>

                        <div class="mobile-form-group">
                            <button class="mobile-btn mobile-btn-primary" style="width: 100%;">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </div>

                    <div class="mobile-store-card">
                        <h6><i class="fas fa-info-circle"></i> حول التطبيق</h6>
                        <p><strong>تطبيق Loacker لدليل المتاجر</strong></p>
                        <p>الإصدار: 1.0.0</p>
                        <p>تم التطوير بواسطة فريق Loacker</p>
                        <div class="mobile-share-buttons">
                            <button class="mobile-share-btn mobile-share-whatsapp">
                                <i class="fab fa-whatsapp"></i> مشاركة التطبيق
                            </button>
                            <button class="mobile-share-btn mobile-share-copy">
                                <i class="fas fa-star"></i> تقييم التطبيق
                            </button>
                        </div>
                    </div>
                </div>

            </div>

        </main>

        <!-- تذييل الصفحة مع التبويبات -->
        <footer class="mobile-tabs">
            <button class="mobile-tab-btn active" data-tab="map">
                <i class="fas fa-map"></i>
                <span>الخريطة</span>
            </button>
            <button class="mobile-tab-btn" data-tab="list">
                <i class="fas fa-list"></i>
                <span>القائمة</span>
            </button>
            <button class="mobile-tab-btn" data-tab="stats">
                <i class="fas fa-chart-bar"></i>
                <span>الإحصائيات</span>
            </button>
            <button class="mobile-tab-btn" data-tab="settings">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </button>
        </footer>

    </div>

    <!-- مكتبات JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <script>
        /**
         * JavaScript موحد ومنظم لواجهة الهاتف المحمول - Loacker
         * جميع الوظائف والتفاعلات في ملف واحد
         */

        // ===== 1. فئة التطبيق الرئيسية =====
        class MobileApp {
            constructor() {
                this.isInitialized = false;
                this.deviceInfo = {};
                this.map = null;
                this.stores = [];
                this.currentListId = null;
                this.lists = [];
                this.markers = [];
                this.selectedLocation = null;
                this.currentRegion = null;

                // التحقق من أن الجهاز هو هاتف محمول
                if (!this.isMobileDevice() || !document.querySelector('.mobile-view')) {
                    console.log('ليس جهاز محمول أو لا توجد واجهة محمولة');
                    return;
                }

                this.init();
            }

            // كشف نوع الجهاز
            isMobileDevice() {
                return document.body.classList.contains('mobile-device') || window.innerWidth <= 768;
            }

            // تهيئة التطبيق
            async init() {
                try {
                    console.log('تهيئة تطبيق الهاتف المحمول...');

                    // كشف معلومات الجهاز
                    this.detectDevice();

                    // تهيئة الواجهة
                    this.setupUI();

                    // تحميل البيانات الأساسية
                    await this.loadInitialData();

                    // تهيئة الخريطة
                    await this.initMap();

                    // إعداد مستمعات الأحداث
                    this.setupEventListeners();

                    this.isInitialized = true;
                    console.log('تم تهيئة تطبيق الهاتف المحمول بنجاح');

                } catch (error) {
                    console.error('خطأ في تهيئة التطبيق:', error);
                    this.showError('حدث خطأ في تحميل التطبيق');
                }
            }

            // كشف معلومات الجهاز
            detectDevice() {
                const userAgent = navigator.userAgent;

                this.deviceInfo = {
                    type: 'mobile',
                    os: this.detectOS(userAgent),
                    browser: this.detectBrowser(userAgent),
                    screenWidth: window.screen.width,
                    screenHeight: window.screen.height,
                    pixelRatio: window.devicePixelRatio || 1,
                    orientation: window.orientation || 0
                };

                // تطبيق فئات CSS حسب نوع الجهاز
                this.applyDeviceClasses();

                console.log('معلومات الجهاز:', this.deviceInfo);
            }

            // كشف نظام التشغيل
            detectOS(userAgent) {
                if (/iPhone|iPad|iPod/i.test(userAgent)) return 'ios';
                if (/Android/i.test(userAgent)) return 'android';
                if (/Windows Phone/i.test(userAgent)) return 'windows';
                return 'unknown';
            }

            // كشف المتصفح
            detectBrowser(userAgent) {
                if (/Chrome/i.test(userAgent)) return 'chrome';
                if (/Safari/i.test(userAgent)) return 'safari';
                if (/Firefox/i.test(userAgent)) return 'firefox';
                if (/Edge/i.test(userAgent)) return 'edge';
                return 'unknown';
            }

            // تطبيق فئات CSS حسب الجهاز
            applyDeviceClasses() {
                const body = document.body;
                const mobileView = document.querySelector('.mobile-view');

                // إضافة فئات نظام التشغيل
                body.classList.add(`${this.deviceInfo.os}-device`);
                if (mobileView) {
                    mobileView.classList.add(`${this.deviceInfo.os}-view`);
                }

                // إضافة فئات حجم الشاشة
                if (this.deviceInfo.screenWidth <= 375) {
                    body.classList.add('small-phone');
                    if (mobileView) mobileView.classList.add('small-phone-view');
                } else if (this.deviceInfo.screenWidth >= 414) {
                    body.classList.add('large-phone');
                    if (mobileView) mobileView.classList.add('large-phone-view');
                }

                // إضافة فئة الاتجاه
                this.updateOrientation();
            }

            // تحديث اتجاه الشاشة
            updateOrientation() {
                const body = document.body;
                const isPortrait = window.innerHeight > window.innerWidth;

                body.classList.toggle('portrait-mode', isPortrait);
                body.classList.toggle('landscape-mode', !isPortrait);
            }

            // ===== 2. إدارة الخريطة =====

            // تهيئة الخريطة
            async initMap() {
                try {
                    // انتظار تحميل مكتبة Leaflet
                    await this.waitForLeaflet();

                    const mapContainer = document.getElementById('mobile-map');
                    if (!mapContainer) {
                        console.log('لم يتم العثور على حاوي الخريطة');
                        return;
                    }

                    // إنشاء الخريطة
                    this.map = L.map('mobile-map', {
                        center: [26.3351, 17.2283], // ليبيا
                        zoom: 6,
                        zoomControl: false,
                        attributionControl: false
                    });

                    // إضافة طبقة الخريطة
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenStreetMap contributors'
                    }).addTo(this.map);

                    // إضافة أزرار التحكم المخصصة
                    this.addMapControls();

                    // تحميل المتاجر على الخريطة
                    this.loadStoresOnMap();

                    console.log('تم تهيئة الخريطة بنجاح');

                } catch (error) {
                    console.error('خطأ في تهيئة الخريطة:', error);
                }
            }

            // انتظار تحميل مكتبة Leaflet
            waitForLeaflet() {
                return new Promise((resolve) => {
                    if (typeof L !== 'undefined') {
                        resolve();
                    } else {
                        const checkInterval = setInterval(() => {
                            if (typeof L !== 'undefined') {
                                clearInterval(checkInterval);
                                resolve();
                            }
                        }, 100);
                    }
                });
            }

            // إضافة أزرار التحكم في الخريطة
            addMapControls() {
                const controlsContainer = document.createElement('div');
                controlsContainer.className = 'mobile-map-controls';

                // زر تكبير
                const zoomInBtn = this.createMapControlButton('fas fa-plus', () => {
                    this.map.zoomIn();
                });

                // زر تصغير
                const zoomOutBtn = this.createMapControlButton('fas fa-minus', () => {
                    this.map.zoomOut();
                });

                // زر الموقع الحالي
                const locationBtn = this.createMapControlButton('fas fa-crosshairs', () => {
                    this.getCurrentLocation();
                });

                controlsContainer.appendChild(zoomInBtn);
                controlsContainer.appendChild(zoomOutBtn);
                controlsContainer.appendChild(locationBtn);

                document.getElementById('mobile-map').appendChild(controlsContainer);
            }

            // إنشاء زر تحكم في الخريطة
            createMapControlButton(iconClass, onClick) {
                const button = document.createElement('button');
                button.className = 'mobile-map-control-btn';
                button.innerHTML = `<i class="${iconClass}"></i>`;
                button.addEventListener('click', onClick);
                return button;
            }

            // الحصول على الموقع الحالي
            getCurrentLocation() {
                if (!navigator.geolocation) {
                    this.showError('الموقع الجغرافي غير مدعوم في هذا المتصفح');
                    return;
                }

                this.showLoading('جاري تحديد موقعك...');

                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;

                        this.map.setView([lat, lng], 15);

                        // إضافة علامة للموقع الحالي
                        if (this.currentLocationMarker) {
                            this.map.removeLayer(this.currentLocationMarker);
                        }

                        this.currentLocationMarker = L.marker([lat, lng], {
                            icon: L.divIcon({
                                className: 'current-location-marker',
                                html: '<i class="fas fa-dot-circle" style="color: #007aff; font-size: 20px;"></i>',
                                iconSize: [20, 20],
                                iconAnchor: [10, 10]
                            })
                        }).addTo(this.map);

                        this.hideLoading();
                        this.showSuccess('تم تحديد موقعك بنجاح');
                    },
                    (error) => {
                        this.hideLoading();
                        this.showError('لا يمكن تحديد موقعك الحالي');
                        console.error('خطأ في الموقع:', error);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 300000
                    }
                );
            }

            // ===== 3. إدارة البيانات =====

            // تحميل البيانات الأساسية
            async loadInitialData() {
                try {
                    // تحميل بيانات تجريبية
                    this.stores = [
                        {
                            id: 1,
                            name: 'متجر Loacker الرئيسي',
                            address: 'طرابلس، ليبيا',
                            phone: '+218912345678',
                            latitude: 32.8872,
                            longitude: 13.1913,
                            region: 'طرابلس'
                        },
                        {
                            id: 2,
                            name: 'فرع Loacker بنغازي',
                            address: 'بنغازي، ليبيا',
                            phone: '+218923456789',
                            latitude: 32.1165,
                            longitude: 20.0686,
                            region: 'بنغازي'
                        },
                        {
                            id: 3,
                            name: 'متجر Loacker مصراتة',
                            address: 'مصراتة، ليبيا',
                            phone: '+218934567890',
                            latitude: 32.3745,
                            longitude: 15.0919,
                            region: 'مصراتة'
                        }
                    ];

                    this.lists = [
                        { id: 1, name: 'المتاجر الرئيسية' },
                        { id: 2, name: 'الفروع الجديدة' }
                    ];

                    this.updateStoresUI();
                    this.updateStats();

                } catch (error) {
                    console.error('خطأ في تحميل البيانات:', error);
                }
            }

            // تحميل المتاجر على الخريطة
            loadStoresOnMap() {
                if (!this.map) return;

                // إزالة العلامات السابقة
                this.markers.forEach(marker => {
                    this.map.removeLayer(marker);
                });
                this.markers = [];

                // إضافة علامات جديدة
                this.stores.forEach(store => {
                    if (store.latitude && store.longitude) {
                        const marker = L.marker([store.latitude, store.longitude])
                            .addTo(this.map);

                        // إضافة نافذة منبثقة
                        const popupContent = this.createStorePopupContent(store);
                        marker.bindPopup(popupContent);

                        // إضافة حدث النقر
                        marker.on('click', () => {
                            this.showStoreDetails(store);
                        });

                        this.markers.push(marker);
                    }
                });

                // تعديل عرض الخريطة لتشمل جميع العلامات
                if (this.markers.length > 0) {
                    const group = new L.featureGroup(this.markers);
                    this.map.fitBounds(group.getBounds().pad(0.1));
                }
            }

            // إنشاء محتوى النافذة المنبثقة للمتجر
            createStorePopupContent(store) {
                return `
                    <div style="text-align: center; font-family: 'Tajawal', sans-serif;">
                        <h6 style="margin-bottom: 10px; color: #d50000;">${store.name}</h6>
                        <p style="margin: 5px 0;"><i class="fas fa-map-marker-alt"></i> ${store.address}</p>
                        <p style="margin: 5px 0;"><i class="fas fa-phone"></i> ${store.phone}</p>
                        <div style="margin-top: 10px;">
                            <button onclick="mobileApp.shareStore(${store.id}, 'whatsapp')"
                                    style="background: #25d366; color: white; border: none; padding: 5px 10px; border-radius: 5px; margin: 2px;">
                                <i class="fab fa-whatsapp"></i> واتساب
                            </button>
                            <button onclick="mobileApp.shareStore(${store.id}, 'copy')"
                                    style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 5px; margin: 2px;">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                        </div>
                    </div>
                `;
            }

            // ===== 4. إدارة واجهة المستخدم =====

            // تهيئة واجهة المستخدم
            setupUI() {
                // إظهار واجهة الهاتف المحمول
                const mobileView = document.querySelector('.mobile-view');
                if (mobileView) {
                    mobileView.style.display = 'flex';
                }

                // إخفاء واجهة سطح المكتب
                const desktopView = document.querySelector('.desktop-view');
                if (desktopView) {
                    desktopView.style.display = 'none';
                }

                // تهيئة التبويبات
                this.setupTabs();
            }

            // تهيئة التبويبات
            setupTabs() {
                const tabButtons = document.querySelectorAll('.mobile-tab-btn');

                tabButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        const tabId = button.dataset.tab;
                        this.switchTab(tabId);
                    });
                });

                // تفعيل التبويب الأول
                if (tabButtons.length > 0) {
                    this.switchTab(tabButtons[0].dataset.tab);
                }
            }

            // تبديل التبويب
            switchTab(tabId) {
                // إزالة الفئة النشطة من جميع الأزرار
                document.querySelectorAll('.mobile-tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                // إضافة الفئة النشطة للزر المحدد
                const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
                if (activeButton) {
                    activeButton.classList.add('active');
                }

                // إخفاء جميع محتويات التبويبات
                document.querySelectorAll('.mobile-tab-content').forEach(content => {
                    content.classList.remove('active');
                    content.style.display = 'none';
                });

                // إظهار المحتوى المحدد
                const activeContent = document.getElementById(`mobile-${tabId}-tab`);
                if (activeContent) {
                    activeContent.classList.add('active');
                    activeContent.style.display = 'block';
                }

                // تنفيذ إجراءات خاصة بكل تبويب
                this.handleTabSwitch(tabId);
            }

            // معالجة تبديل التبويب
            handleTabSwitch(tabId) {
                switch (tabId) {
                    case 'map':
                        // إعادة تحجيم الخريطة
                        setTimeout(() => {
                            if (this.map) {
                                this.map.invalidateSize();
                            }
                        }, 100);
                        break;

                    case 'list':
                        // تحديث قائمة المتاجر
                        this.updateStoresUI();
                        break;

                    case 'stats':
                        // تحديث الإحصائيات
                        this.updateStats();
                        break;
                }
            }

            // ===== 5. وظائف المشاركة =====

            // مشاركة متجر
            shareStore(storeId, method) {
                const store = this.stores.find(s => s.id === storeId);
                if (!store) {
                    this.showError('لم يتم العثور على المتجر');
                    return;
                }

                const shareText = this.generateShareText(store);

                switch (method) {
                    case 'whatsapp':
                        this.shareViaWhatsApp(shareText);
                        break;
                    case 'copy':
                        this.copyToClipboard(shareText);
                        break;
                }
            }

            // إنشاء نص المشاركة
            generateShareText(store) {
                return `🏪 ${store.name}\n📍 ${store.address}\n📞 ${store.phone}\n\n🗺️ الموقع على الخريطة:\nhttps://maps.google.com/?q=${store.latitude},${store.longitude}`;
            }

            // مشاركة عبر واتساب
            shareViaWhatsApp(text) {
                const encodedText = encodeURIComponent(text);
                const whatsappUrl = `https://wa.me/?text=${encodedText}`;
                window.open(whatsappUrl, '_blank');
            }

            // نسخ إلى الحافظة
            async copyToClipboard(text) {
                try {
                    if (navigator.clipboard) {
                        await navigator.clipboard.writeText(text);
                        this.showSuccess('تم نسخ المعلومات بنجاح');
                    } else {
                        // طريقة بديلة للمتصفحات القديمة
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        this.showSuccess('تم نسخ المعلومات بنجاح');
                    }
                } catch (error) {
                    console.error('خطأ في النسخ:', error);
                    this.showError('فشل في نسخ المعلومات');
                }
            }

            // ===== 6. إدارة الإشعارات =====

            // إظهار رسالة تحميل
            showLoading(message = 'جاري التحميل...') {
                const existingLoader = document.querySelector('.mobile-loading');
                if (existingLoader) {
                    existingLoader.remove();
                }

                const loader = document.createElement('div');
                loader.className = 'mobile-loading';
                loader.innerHTML = `
                    <div class="mobile-loading-spinner"></div>
                    <span>${message}</span>
                `;

                document.querySelector('.mobile-content').appendChild(loader);
            }

            // إخفاء رسالة التحميل
            hideLoading() {
                const loader = document.querySelector('.mobile-loading');
                if (loader) {
                    loader.remove();
                }
            }

            // إظهار رسالة نجاح
            showSuccess(message) {
                this.showToast(message, 'success');
            }

            // إظهار رسالة خطأ
            showError(message) {
                this.showToast(message, 'error');
            }

            // إظهار رسالة منبثقة
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `mobile-toast mobile-toast-${type}`;
                toast.textContent = message;

                document.body.appendChild(toast);

                // إظهار الرسالة
                setTimeout(() => {
                    toast.classList.add('show');
                }, 100);

                // إخفاء الرسالة بعد 3 ثوان
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }

            // ===== 7. إدارة الأحداث =====

            // إعداد مستمعات الأحداث
            setupEventListeners() {
                // مستمع تغيير الاتجاه
                window.addEventListener('orientationchange', () => {
                    setTimeout(() => {
                        this.updateOrientation();
                        if (this.map) {
                            this.map.invalidateSize();
                        }
                    }, 100);
                });

                // مستمع تغيير حجم النافذة
                window.addEventListener('resize', () => {
                    this.updateOrientation();
                    if (this.map) {
                        this.map.invalidateSize();
                    }
                });

                // مستمع للبحث
                const searchInput = document.getElementById('mobile-search-input');
                if (searchInput) {
                    searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
                }

                // مستمع للفلاتر
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('mobile-filter-btn')) {
                        this.handleFilterClick(e.target);
                    }
                });
            }

            // معالجة البحث
            handleSearch(e) {
                const query = e.target.value.toLowerCase().trim();

                if (query === '') {
                    this.displayStores(this.stores);
                    return;
                }

                const filteredStores = this.stores.filter(store =>
                    store.name.toLowerCase().includes(query) ||
                    store.address.toLowerCase().includes(query) ||
                    store.phone.includes(query)
                );

                this.displayStores(filteredStores);
            }

            // معالجة النقر على الفلاتر
            handleFilterClick(button) {
                // إزالة الفئة النشطة من جميع الفلاتر
                document.querySelectorAll('.mobile-filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                // إضافة الفئة النشطة للفلتر المحدد
                button.classList.add('active');

                const filterType = button.dataset.filter;
                this.applyFilter(filterType);
            }

            // تطبيق الفلتر
            applyFilter(filterType) {
                let filteredStores = [...this.stores];

                switch (filterType) {
                    case 'all':
                        // عرض جميع المتاجر
                        break;
                    case 'nearby':
                        // عرض المتاجر القريبة
                        this.showToast('ميزة المتاجر القريبة ستتوفر قريباً');
                        break;
                    case 'favorites':
                        // عرض المتاجر المفضلة
                        this.showToast('ميزة المفضلة ستتوفر قريباً');
                        break;
                }

                this.displayStores(filteredStores);
            }

            // ===== 8. وظائف مساعدة =====

            // تأخير تنفيذ الدالة
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // تحديث واجهة المتاجر
            updateStoresUI() {
                this.displayStores(this.stores);
            }

            // عرض المتاجر
            displayStores(stores) {
                const storesContainer = document.getElementById('mobile-stores-container');
                if (!storesContainer) return;

                storesContainer.innerHTML = '';

                if (stores.length === 0) {
                    storesContainer.innerHTML = `
                        <div class="mobile-no-results">
                            <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                            <p>لا توجد متاجر مطابقة للبحث</p>
                        </div>
                    `;
                    return;
                }

                stores.forEach(store => {
                    const storeCard = this.createStoreCard(store);
                    storesContainer.appendChild(storeCard);
                });
            }

            // إنشاء بطاقة متجر
            createStoreCard(store) {
                const card = document.createElement('div');
                card.className = 'mobile-store-card';
                card.innerHTML = `
                    <div class="store-name">${store.name}</div>
                    <div class="store-address">
                        <i class="fas fa-map-marker-alt"></i> ${store.address}
                    </div>
                    <div class="store-phone">
                        <a href="tel:${store.phone}" class="store-phone">
                            <i class="fas fa-phone"></i> ${store.phone}
                        </a>
                    </div>
                    <div class="mobile-share-buttons">
                        <button class="mobile-share-btn mobile-share-whatsapp" onclick="mobileApp.shareStore(${store.id}, 'whatsapp')">
                            <i class="fab fa-whatsapp"></i> واتساب
                        </button>
                        <button class="mobile-share-btn mobile-share-copy" onclick="mobileApp.shareStore(${store.id}, 'copy')">
                            <i class="fas fa-copy"></i> نسخ
                        </button>
                    </div>
                `;

                // إضافة حدث النقر لعرض تفاصيل المتجر
                card.addEventListener('click', (e) => {
                    if (!e.target.closest('button')) {
                        this.showStoreDetails(store);
                    }
                });

                return card;
            }

            // عرض تفاصيل المتجر
            showStoreDetails(store) {
                // التبديل إلى تبويب الخريطة
                this.switchTab('map');

                // التركيز على المتجر في الخريطة
                if (this.map && store.latitude && store.longitude) {
                    this.map.setView([store.latitude, store.longitude], 15);

                    // العثور على العلامة المقابلة وفتح النافذة المنبثقة
                    const marker = this.markers.find(m =>
                        m.getLatLng().lat === store.latitude &&
                        m.getLatLng().lng === store.longitude
                    );

                    if (marker) {
                        marker.openPopup();
                    }
                }
            }

            // تحديث الإحصائيات
            updateStats() {
                const totalStores = this.stores.length;
                const regions = [...new Set(this.stores.map(store => store.region))].length;
                const avgStoresPerRegion = regions > 0 ? (totalStores / regions).toFixed(1) : 0;

                // تحديث العدادات
                this.updateCounter('mobile-total-stores-count', totalStores);
                this.updateCounter('mobile-regions-count', regions);
                this.updateCounter('mobile-avg-stores-per-region', avgStoresPerRegion);

                // تحديث العدادات في تبويب الإحصائيات
                this.updateCounter('mobile-total-stores-count-2', totalStores);
                this.updateCounter('mobile-regions-count-2', regions);
                this.updateCounter('mobile-avg-stores-per-region-2', avgStoresPerRegion);
            }

            // تحديث عداد
            updateCounter(elementId, value) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = value;
                }
            }
        }

        // ===== تهيئة التطبيق =====

        // تهيئة التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            // إنشاء متغير عام للتطبيق
            window.mobileApp = new MobileApp();
        });

        // ===== سكريبت إضافي لكشف الجهاز =====

        // كشف نوع الجهاز وتطبيق الفئات المناسبة
        (function() {
            const userAgent = navigator.userAgent;
            const body = document.body;

            // كشف نوع الجهاز
            if (/iPhone/i.test(userAgent)) {
                body.classList.add('iphone-device');
            } else if (/iPad/i.test(userAgent)) {
                body.classList.add('ipad-device');
            } else if (/Android/i.test(userAgent)) {
                body.classList.add('android-device');

                // كشف العلامة التجارية للأندرويد
                if (/Xiaomi|Mi |Redmi/i.test(userAgent)) {
                    body.classList.add('xiaomi-device');
                } else if (/Samsung|SM-/i.test(userAgent)) {
                    body.classList.add('samsung-device');
                } else if (/Huawei|Honor/i.test(userAgent)) {
                    body.classList.add('huawei-device');
                }
            }

            // كشف حجم الشاشة
            const screenWidth = window.screen.width;
            if (screenWidth <= 375) {
                body.classList.add('small-phone');
            } else if (screenWidth >= 414) {
                body.classList.add('large-phone');
            }

            // كشف الاتجاه
            function updateOrientation() {
                const isPortrait = window.innerHeight > window.innerWidth;
                body.classList.toggle('portrait-mode', isPortrait);
                body.classList.toggle('landscape-mode', !isPortrait);
            }

            updateOrientation();
            window.addEventListener('orientationchange', () => {
                setTimeout(updateOrientation, 100);
            });
            window.addEventListener('resize', updateOrientation);

            // تعيين متغيرات CSS للجهاز
            document.documentElement.style.setProperty('--device-width', window.screen.width + 'px');
            document.documentElement.style.setProperty('--device-height', window.screen.height + 'px');
            document.documentElement.style.setProperty('--device-pixel-ratio', window.devicePixelRatio || 1);

            // تعيين المناطق الآمنة للهواتف الحديثة
            if (CSS.supports('padding: env(safe-area-inset-top)')) {
                document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)');
                document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)');
            }
        })();

        // منع التكبير عند النقر المزدوج
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // منع التمرير المطاطي في iOS
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('.mobile-content') || e.target.closest('.mobile-map')) {
                return;
            }
            e.preventDefault();
        }, { passive: false });
    </script>

</body>
</html>
