/**
 * ملف JavaScript موحد ومنظم لواجهة الهاتف المحمول - Loacker
 * يجمع جميع الوظائف والتفاعلات المطلوبة للهواتف المحمولة
 * تم إنشاؤه: 2024
 * 
 * المحتويات:
 * 1. كشف الأجهزة والتهيئة
 * 2. إدارة الخريطة للهاتف المحمول
 * 3. إدارة المتاجر والبيانات
 * 4. التفاعل مع واجهة المستخدم
 * 5. إدارة الإشعارات والرسائل
 * 6. وظائف المشاركة والتصدير
 * 7. إدارة الأحداث والمستمعات
 * 8. وظائف مساعدة ومرافق
 */

// ===== 1. كشف الأجهزة والتهيئة =====

class MobileApp {
    constructor() {
        this.isInitialized = false;
        this.deviceInfo = {};
        this.map = null;
        this.stores = [];
        this.currentListId = null;
        this.lists = [];
        this.markers = [];
        this.selectedLocation = null;
        this.currentRegion = null;
        
        // التحقق من أن الجهاز هو هاتف محمول
        if (!this.isMobileDevice() || !document.querySelector('.mobile-view')) {
            console.log('ليس جهاز محمول أو لا توجد واجهة محمولة');
            return;
        }
        
        this.init();
    }
    
    // كشف نوع الجهاز
    isMobileDevice() {
        return document.body.classList.contains('mobile-device');
    }
    
    // تهيئة التطبيق
    async init() {
        try {
            console.log('تهيئة تطبيق الهاتف المحمول...');
            
            // كشف معلومات الجهاز
            this.detectDevice();
            
            // تهيئة الواجهة
            this.setupUI();
            
            // تحميل البيانات الأساسية
            await this.loadInitialData();
            
            // تهيئة الخريطة
            await this.initMap();
            
            // إعداد مستمعات الأحداث
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('تم تهيئة تطبيق الهاتف المحمول بنجاح');
            
        } catch (error) {
            console.error('خطأ في تهيئة التطبيق:', error);
            this.showError('حدث خطأ في تحميل التطبيق');
        }
    }
    
    // كشف معلومات الجهاز
    detectDevice() {
        const userAgent = navigator.userAgent;
        
        this.deviceInfo = {
            type: 'mobile',
            os: this.detectOS(userAgent),
            browser: this.detectBrowser(userAgent),
            screenWidth: window.screen.width,
            screenHeight: window.screen.height,
            pixelRatio: window.devicePixelRatio || 1,
            orientation: window.orientation || 0
        };
        
        // تطبيق فئات CSS حسب نوع الجهاز
        this.applyDeviceClasses();
        
        console.log('معلومات الجهاز:', this.deviceInfo);
    }
    
    // كشف نظام التشغيل
    detectOS(userAgent) {
        if (/iPhone|iPad|iPod/i.test(userAgent)) return 'ios';
        if (/Android/i.test(userAgent)) return 'android';
        if (/Windows Phone/i.test(userAgent)) return 'windows';
        return 'unknown';
    }
    
    // كشف المتصفح
    detectBrowser(userAgent) {
        if (/Chrome/i.test(userAgent)) return 'chrome';
        if (/Safari/i.test(userAgent)) return 'safari';
        if (/Firefox/i.test(userAgent)) return 'firefox';
        if (/Edge/i.test(userAgent)) return 'edge';
        return 'unknown';
    }
    
    // تطبيق فئات CSS حسب الجهاز
    applyDeviceClasses() {
        const body = document.body;
        const mobileView = document.querySelector('.mobile-view');
        
        // إضافة فئات نظام التشغيل
        body.classList.add(`${this.deviceInfo.os}-device`);
        if (mobileView) {
            mobileView.classList.add(`${this.deviceInfo.os}-view`);
        }
        
        // إضافة فئات حجم الشاشة
        if (this.deviceInfo.screenWidth <= 375) {
            body.classList.add('small-phone');
            if (mobileView) mobileView.classList.add('small-phone-view');
        } else if (this.deviceInfo.screenWidth >= 414) {
            body.classList.add('large-phone');
            if (mobileView) mobileView.classList.add('large-phone-view');
        }
        
        // إضافة فئة الاتجاه
        this.updateOrientation();
    }
    
    // تحديث اتجاه الشاشة
    updateOrientation() {
        const body = document.body;
        const isPortrait = window.innerHeight > window.innerWidth;
        
        body.classList.toggle('portrait-mode', isPortrait);
        body.classList.toggle('landscape-mode', !isPortrait);
    }
    
    // ===== 2. إدارة الخريطة للهاتف المحمول =====
    
    // تهيئة الخريطة
    async initMap() {
        try {
            // انتظار تحميل مكتبة Leaflet
            await this.waitForLeaflet();
            
            const mapContainer = document.getElementById('mobile-map');
            if (!mapContainer) {
                console.log('لم يتم العثور على حاوي الخريطة');
                return;
            }
            
            // إنشاء الخريطة
            this.map = L.map('mobile-map', {
                center: [26.3351, 17.2283], // ليبيا
                zoom: 6,
                zoomControl: false,
                attributionControl: false
            });
            
            // إضافة طبقة الخريطة
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(this.map);
            
            // إضافة أزرار التحكم المخصصة
            this.addMapControls();
            
            // تحميل المتاجر على الخريطة
            this.loadStoresOnMap();
            
            console.log('تم تهيئة الخريطة بنجاح');
            
        } catch (error) {
            console.error('خطأ في تهيئة الخريطة:', error);
        }
    }
    
    // انتظار تحميل مكتبة Leaflet
    waitForLeaflet() {
        return new Promise((resolve) => {
            if (typeof L !== 'undefined') {
                resolve();
            } else {
                const checkInterval = setInterval(() => {
                    if (typeof L !== 'undefined') {
                        clearInterval(checkInterval);
                        resolve();
                    }
                }, 100);
            }
        });
    }
    
    // إضافة أزرار التحكم في الخريطة
    addMapControls() {
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'mobile-map-controls';
        
        // زر تكبير
        const zoomInBtn = this.createMapControlButton('fas fa-plus', () => {
            this.map.zoomIn();
        });
        
        // زر تصغير
        const zoomOutBtn = this.createMapControlButton('fas fa-minus', () => {
            this.map.zoomOut();
        });
        
        // زر الموقع الحالي
        const locationBtn = this.createMapControlButton('fas fa-crosshairs', () => {
            this.getCurrentLocation();
        });
        
        controlsContainer.appendChild(zoomInBtn);
        controlsContainer.appendChild(zoomOutBtn);
        controlsContainer.appendChild(locationBtn);
        
        document.getElementById('mobile-map').appendChild(controlsContainer);
    }
    
    // إنشاء زر تحكم في الخريطة
    createMapControlButton(iconClass, onClick) {
        const button = document.createElement('button');
        button.className = 'mobile-map-control-btn';
        button.innerHTML = `<i class="${iconClass}"></i>`;
        button.addEventListener('click', onClick);
        return button;
    }
    
    // الحصول على الموقع الحالي
    getCurrentLocation() {
        if (!navigator.geolocation) {
            this.showError('الموقع الجغرافي غير مدعوم في هذا المتصفح');
            return;
        }
        
        this.showLoading('جاري تحديد موقعك...');
        
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                
                this.map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (this.currentLocationMarker) {
                    this.map.removeLayer(this.currentLocationMarker);
                }
                
                this.currentLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'current-location-marker',
                        html: '<i class="fas fa-dot-circle" style="color: #007aff; font-size: 20px;"></i>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(this.map);
                
                this.hideLoading();
                this.showSuccess('تم تحديد موقعك بنجاح');
            },
            (error) => {
                this.hideLoading();
                this.showError('لا يمكن تحديد موقعك الحالي');
                console.error('خطأ في الموقع:', error);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000
            }
        );
    }
    
    // ===== 3. إدارة المتاجر والبيانات =====
    
    // تحميل البيانات الأساسية
    async loadInitialData() {
        try {
            // تحميل القوائم المخصصة
            await this.loadLists();
            
            // تحميل المتاجر
            await this.loadStores();
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            this.showError('حدث خطأ في تحميل البيانات');
        }
    }
    
    // تحميل القوائم المخصصة
    async loadLists() {
        try {
            const response = await fetch('/api/custom-lists');
            const data = await response.json();
            
            if (data.success) {
                this.lists = data.lists;
                this.updateListsUI();
                console.log('تم تحميل القوائم:', this.lists);
            }
        } catch (error) {
            console.error('خطأ في تحميل القوائم:', error);
        }
    }
    
    // تحميل المتاجر
    async loadStores(listId = null, region = null) {
        try {
            this.showLoading('جاري تحميل المتاجر...');
            
            let url = '/api/stores';
            const params = new URLSearchParams();
            
            if (listId) params.append('list_id', listId);
            if (region) params.append('region', region);
            
            if (params.toString()) {
                url += '?' + params.toString();
            }
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.stores = data.stores;
                this.updateStoresUI();
                this.loadStoresOnMap();
                console.log(`تم تحميل ${this.stores.length} متجر`);
            } else {
                this.showError(data.error || 'حدث خطأ في تحميل المتاجر');
            }
            
        } catch (error) {
            console.error('خطأ في تحميل المتاجر:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        } finally {
            this.hideLoading();
        }
    }
    
    // تحميل المتاجر على الخريطة
    loadStoresOnMap() {
        if (!this.map) return;
        
        // إزالة العلامات السابقة
        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.markers = [];
        
        // إضافة علامات جديدة
        this.stores.forEach(store => {
            if (store.latitude && store.longitude) {
                const marker = L.marker([store.latitude, store.longitude])
                    .addTo(this.map);
                
                // إضافة نافذة منبثقة
                const popupContent = this.createStorePopupContent(store);
                marker.bindPopup(popupContent);
                
                // إضافة حدث النقر
                marker.on('click', () => {
                    this.showStoreDetails(store);
                });
                
                this.markers.push(marker);
            }
        });
        
        // تعديل عرض الخريطة لتشمل جميع العلامات
        if (this.markers.length > 0) {
            const group = new L.featureGroup(this.markers);
            this.map.fitBounds(group.getBounds().pad(0.1));
        }
    }
    
    // إنشاء محتوى النافذة المنبثقة للمتجر
    createStorePopupContent(store) {
        return `
            <div class="mobile-store-popup-content">
                <h6>${store.name}</h6>
                <p><i class="fas fa-map-marker-alt"></i> ${store.address}</p>
                <p><i class="fas fa-phone"></i> ${store.phone}</p>
                <div class="mobile-share-buttons">
                    <button class="mobile-share-btn mobile-share-whatsapp" onclick="mobileApp.shareStore(${store.id}, 'whatsapp')">
                        <i class="fab fa-whatsapp"></i> واتساب
                    </button>
                    <button class="mobile-share-btn mobile-share-copy" onclick="mobileApp.shareStore(${store.id}, 'copy')">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                </div>
            </div>
        `;
    }
    
    // ===== 4. التفاعل مع واجهة المستخدم =====
    
    // تهيئة واجهة المستخدم
    setupUI() {
        // إظهار واجهة الهاتف المحمول
        const mobileView = document.querySelector('.mobile-view');
        if (mobileView) {
            mobileView.style.display = 'flex';
        }
        
        // إخفاء واجهة سطح المكتب
        const desktopView = document.querySelector('.desktop-view');
        if (desktopView) {
            desktopView.style.display = 'none';
        }
        
        // تهيئة التبويبات
        this.setupTabs();
        
        // تهيئة البحث
        this.setupSearch();
        
        // تهيئة الفلاتر
        this.setupFilters();
    }
    
    // تهيئة التبويبات
    setupTabs() {
        const tabButtons = document.querySelectorAll('.mobile-tab-btn');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const tabId = button.dataset.tab;
                this.switchTab(tabId);
            });
        });
        
        // تفعيل التبويب الأول
        if (tabButtons.length > 0) {
            this.switchTab(tabButtons[0].dataset.tab);
        }
    }
    
    // تبديل التبويب
    switchTab(tabId) {
        // إزالة الفئة النشطة من جميع الأزرار
        document.querySelectorAll('.mobile-tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // إضافة الفئة النشطة للزر المحدد
        const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
        if (activeButton) {
            activeButton.classList.add('active');
        }
        
        // إخفاء جميع محتويات التبويبات
        document.querySelectorAll('.mobile-tab-content').forEach(content => {
            content.style.display = 'none';
        });
        
        // إظهار المحتوى المحدد
        const activeContent = document.getElementById(`mobile-${tabId}-tab`);
        if (activeContent) {
            activeContent.style.display = 'block';
        }
        
        // تنفيذ إجراءات خاصة بكل تبويب
        this.handleTabSwitch(tabId);
    }
    
    // معالجة تبديل التبويب
    handleTabSwitch(tabId) {
        switch (tabId) {
            case 'map':
                // إعادة تحجيم الخريطة
                setTimeout(() => {
                    if (this.map) {
                        this.map.invalidateSize();
                    }
                }, 100);
                break;
                
            case 'list':
                // تحديث قائمة المتاجر
                this.updateStoresUI();
                break;
                
            case 'stats':
                // تحديث الإحصائيات
                this.updateStats();
                break;
        }
    }
    
    // ===== 5. إدارة الإشعارات والرسائل =====
    
    // إظهار رسالة تحميل
    showLoading(message = 'جاري التحميل...') {
        const existingLoader = document.querySelector('.mobile-loading');
        if (existingLoader) {
            existingLoader.remove();
        }
        
        const loader = document.createElement('div');
        loader.className = 'mobile-loading';
        loader.innerHTML = `
            <div class="mobile-loading-spinner"></div>
            <span>${message}</span>
        `;
        
        document.querySelector('.mobile-content').appendChild(loader);
    }
    
    // إخفاء رسالة التحميل
    hideLoading() {
        const loader = document.querySelector('.mobile-loading');
        if (loader) {
            loader.remove();
        }
    }
    
    // إظهار رسالة نجاح
    showSuccess(message) {
        this.showToast(message, 'success');
    }
    
    // إظهار رسالة خطأ
    showError(message) {
        this.showToast(message, 'error');
    }
    
    // إظهار رسالة منبثقة
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `mobile-toast mobile-toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // إظهار الرسالة
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // إخفاء الرسالة بعد 3 ثوان
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // ===== 6. وظائف المشاركة والتصدير =====

    // مشاركة متجر
    shareStore(storeId, method) {
        const store = this.stores.find(s => s.id === storeId);
        if (!store) {
            this.showError('لم يتم العثور على المتجر');
            return;
        }

        const shareText = this.generateShareText(store);

        switch (method) {
            case 'whatsapp':
                this.shareViaWhatsApp(shareText);
                break;
            case 'copy':
                this.copyToClipboard(shareText);
                break;
            case 'native':
                this.shareViaNativeAPI(shareText);
                break;
        }
    }

    // إنشاء نص المشاركة
    generateShareText(store) {
        return `🏪 ${store.name}\n📍 ${store.address}\n📞 ${store.phone}\n\n🗺️ الموقع على الخريطة:\nhttps://maps.google.com/?q=${store.latitude},${store.longitude}`;
    }

    // مشاركة عبر واتساب
    shareViaWhatsApp(text) {
        const encodedText = encodeURIComponent(text);
        const whatsappUrl = `https://wa.me/?text=${encodedText}`;
        window.open(whatsappUrl, '_blank');
    }

    // نسخ إلى الحافظة
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(text);
                this.showSuccess('تم نسخ المعلومات بنجاح');
            } else {
                // طريقة بديلة للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showSuccess('تم نسخ المعلومات بنجاح');
            }
        } catch (error) {
            console.error('خطأ في النسخ:', error);
            this.showError('فشل في نسخ المعلومات');
        }
    }

    // مشاركة عبر واجهة المشاركة الأصلية
    async shareViaNativeAPI(text) {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: 'معلومات المتجر - Loacker',
                    text: text
                });
            } catch (error) {
                console.error('خطأ في المشاركة:', error);
            }
        } else {
            // العودة إلى النسخ
            this.copyToClipboard(text);
        }
    }

    // ===== 7. إدارة الأحداث والمستمعات =====

    // إعداد مستمعات الأحداث
    setupEventListeners() {
        // مستمع تغيير الاتجاه
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.updateOrientation();
                if (this.map) {
                    this.map.invalidateSize();
                }
            }, 100);
        });

        // مستمع تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.updateOrientation();
            if (this.map) {
                this.map.invalidateSize();
            }
        });

        // مستمع الضغط على زر الرجوع
        window.addEventListener('popstate', (e) => {
            this.handleBackButton(e);
        });

        // مستمع لمنع التكبير المزدوج
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });

        // مستمع للبحث
        const searchInput = document.getElementById('mobile-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        }

        // مستمع للفلاتر
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('mobile-filter-btn')) {
                this.handleFilterClick(e.target);
            }
        });
    }

    // معالجة زر الرجوع
    handleBackButton(e) {
        // إغلاق النوافذ المنبثقة المفتوحة
        const openPopup = document.querySelector('.mobile-store-popup.show');
        if (openPopup) {
            openPopup.classList.remove('show');
            e.preventDefault();
            return;
        }

        // العودة إلى التبويب الرئيسي
        const activeTab = document.querySelector('.mobile-tab-btn.active');
        if (activeTab && activeTab.dataset.tab !== 'map') {
            this.switchTab('map');
            e.preventDefault();
        }
    }

    // معالجة بداية اللمس
    handleTouchStart(e) {
        this.lastTouchTime = Date.now();
    }

    // معالجة نهاية اللمس
    handleTouchEnd(e) {
        const now = Date.now();
        if (now - this.lastTouchTime < 300) {
            // منع التكبير المزدوج
            e.preventDefault();
        }
    }

    // معالجة البحث
    handleSearch(e) {
        const query = e.target.value.toLowerCase().trim();

        if (query === '') {
            this.showAllStores();
            return;
        }

        const filteredStores = this.stores.filter(store =>
            store.name.toLowerCase().includes(query) ||
            store.address.toLowerCase().includes(query) ||
            store.phone.includes(query)
        );

        this.displayStores(filteredStores);
    }

    // معالجة النقر على الفلاتر
    handleFilterClick(button) {
        // إزالة الفئة النشطة من جميع الفلاتر
        document.querySelectorAll('.mobile-filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إضافة الفئة النشطة للفلتر المحدد
        button.classList.add('active');

        const filterType = button.dataset.filter;
        this.applyFilter(filterType);
    }

    // تطبيق الفلتر
    applyFilter(filterType) {
        let filteredStores = [...this.stores];

        switch (filterType) {
            case 'all':
                // عرض جميع المتاجر
                break;
            case 'nearby':
                // عرض المتاجر القريبة (يتطلب الموقع الحالي)
                filteredStores = this.filterNearbyStores();
                break;
            case 'favorites':
                // عرض المتاجر المفضلة
                filteredStores = this.filterFavoriteStores();
                break;
            default:
                // فلتر حسب المنطقة
                filteredStores = this.stores.filter(store =>
                    store.region === filterType
                );
        }

        this.displayStores(filteredStores);
    }

    // ===== 8. وظائف مساعدة ومرافق =====

    // تأخير تنفيذ الدالة
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // تحديث واجهة القوائم
    updateListsUI() {
        const listsContainer = document.getElementById('mobile-lists-container');
        if (!listsContainer) return;

        listsContainer.innerHTML = '';

        // إضافة خيار "جميع المتاجر"
        const allOption = document.createElement('button');
        allOption.className = 'mobile-filter-btn active';
        allOption.dataset.filter = 'all';
        allOption.textContent = 'جميع المتاجر';
        listsContainer.appendChild(allOption);

        // إضافة القوائم المخصصة
        this.lists.forEach(list => {
            const listButton = document.createElement('button');
            listButton.className = 'mobile-filter-btn';
            listButton.dataset.filter = list.id;
            listButton.textContent = list.name;
            listsContainer.appendChild(listButton);
        });
    }

    // تحديث واجهة المتاجر
    updateStoresUI() {
        this.displayStores(this.stores);
    }

    // عرض المتاجر
    displayStores(stores) {
        const storesContainer = document.getElementById('mobile-stores-container');
        if (!storesContainer) return;

        storesContainer.innerHTML = '';

        if (stores.length === 0) {
            storesContainer.innerHTML = `
                <div class="mobile-no-results">
                    <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                    <p>لا توجد متاجر مطابقة للبحث</p>
                </div>
            `;
            return;
        }

        stores.forEach(store => {
            const storeCard = this.createStoreCard(store);
            storesContainer.appendChild(storeCard);
        });
    }

    // إنشاء بطاقة متجر
    createStoreCard(store) {
        const card = document.createElement('div');
        card.className = 'mobile-store-card';
        card.innerHTML = `
            <div class="store-name">${store.name}</div>
            <div class="store-address">
                <i class="fas fa-map-marker-alt"></i> ${store.address}
            </div>
            <div class="store-phone">
                <a href="tel:${store.phone}" class="store-phone">
                    <i class="fas fa-phone"></i> ${store.phone}
                </a>
            </div>
            <div class="mobile-share-buttons">
                <button class="mobile-share-btn mobile-share-whatsapp" onclick="mobileApp.shareStore(${store.id}, 'whatsapp')">
                    <i class="fab fa-whatsapp"></i> واتساب
                </button>
                <button class="mobile-share-btn mobile-share-copy" onclick="mobileApp.shareStore(${store.id}, 'copy')">
                    <i class="fas fa-copy"></i> نسخ
                </button>
            </div>
        `;

        // إضافة حدث النقر لعرض تفاصيل المتجر
        card.addEventListener('click', (e) => {
            if (!e.target.closest('button')) {
                this.showStoreDetails(store);
            }
        });

        return card;
    }

    // عرض تفاصيل المتجر
    showStoreDetails(store) {
        // التبديل إلى تبويب الخريطة
        this.switchTab('map');

        // التركيز على المتجر في الخريطة
        if (this.map && store.latitude && store.longitude) {
            this.map.setView([store.latitude, store.longitude], 15);

            // العثور على العلامة المقابلة وفتح النافذة المنبثقة
            const marker = this.markers.find(m =>
                m.getLatLng().lat === store.latitude &&
                m.getLatLng().lng === store.longitude
            );

            if (marker) {
                marker.openPopup();
            }
        }
    }

    // تحديث الإحصائيات
    updateStats() {
        const totalStores = this.stores.length;
        const regions = [...new Set(this.stores.map(store => store.region))].length;
        const avgStoresPerRegion = regions > 0 ? (totalStores / regions).toFixed(1) : 0;

        // تحديث العدادات
        this.updateCounter('mobile-total-stores-count', totalStores);
        this.updateCounter('mobile-regions-count', regions);
        this.updateCounter('mobile-avg-stores-per-region', avgStoresPerRegion);
    }

    // تحديث عداد
    updateCounter(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    // عرض جميع المتاجر
    showAllStores() {
        this.displayStores(this.stores);
    }

    // فلترة المتاجر القريبة
    filterNearbyStores() {
        // يتطلب تنفيذ منطق حساب المسافة
        return this.stores;
    }

    // فلترة المتاجر المفضلة
    filterFavoriteStores() {
        // يتطلب تنفيذ نظام المفضلة
        return this.stores;
    }

    // تهيئة البحث
    setupSearch() {
        const searchContainer = document.getElementById('mobile-search-container');
        if (searchContainer) {
            searchContainer.innerHTML = `
                <div class="mobile-search-bar">
                    <i class="fas fa-search mobile-search-icon"></i>
                    <input type="text" id="mobile-search-input" class="mobile-search-input" placeholder="البحث في المتاجر...">
                </div>
            `;
        }
    }

    // تهيئة الفلاتر
    setupFilters() {
        const filtersContainer = document.getElementById('mobile-filters-container');
        if (filtersContainer) {
            filtersContainer.innerHTML = `
                <div class="mobile-filters">
                    <button class="mobile-filter-btn active" data-filter="all">الكل</button>
                    <button class="mobile-filter-btn" data-filter="nearby">القريبة</button>
                    <button class="mobile-filter-btn" data-filter="favorites">المفضلة</button>
                </div>
            `;
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء متغير عام للتطبيق
    window.mobileApp = new MobileApp();
});

// تصدير الفئة للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileApp;
}
