# ملفات نموذج الهاتف المحمول الموحدة - Loacker

## نظرة عامة

هذا المجلد يحتوي على جميع الملفات الموحدة والمنظمة لواجهة الهاتف المحمول في تطبيق Loacker. تم تجميع جميع الملفات المتناثرة في ملفات موحدة ومنظمة لسهولة الصيانة والتطوير.

## الملفات المتضمنة

### 1. `mobile-unified.css`
ملف CSS شامل يحتوي على جميع التنسيقات المطلوبة للهاتف المحمول:

**المحتويات:**
- المتغيرات الأساسية (الألوان، الأبعاد، الانتقالات)
- كشف الأجهزة والتوافق (iPhone, Android, Samsung, Xiaomi)
- الهيكل الأساسي للواجهة
- أنماط الرأس والتذييل
- أنماط المحتوى والبطاقات
- أنماط الأزرار والتفاعل
- أنماط الخريطة والموقع
- أنماط النماذج والمدخلات
- أنماط الإشعارات والرسائل
- إصلاحات خاصة بالأجهزة
- استعلامات الوسائط للاستجابة
- أنماط المكونات المتقدمة
- أنماط الرسوم المتحركة
- أنماط الطباعة

### 2. `mobile-unified.js`
ملف JavaScript شامل يحتوي على جميع الوظائف المطلوبة للهاتف المحمول:

**المحتويات:**
- كشف الأجهزة والتهيئة
- إدارة الخريطة للهاتف المحمول
- إدارة المتاجر والبيانات
- التفاعل مع واجهة المستخدم
- إدارة الإشعارات والرسائل
- وظائف المشاركة والتصدير
- إدارة الأحداث والمستمعات
- وظائف مساعدة ومرافق

### 3. `mobile-unified-template.html`
قالب HTML كامل يوضح كيفية استخدام الملفات الموحدة:

**المميزات:**
- هيكل HTML كامل للهاتف المحمول
- تكامل مع الملفات الموحدة
- أمثلة على جميع المكونات
- إعدادات الجهاز التلقائية
- دعم المناطق الآمنة

### 4. `mobile-unified-README.md`
هذا الملف - دليل شامل للاستخدام والتطوير

## كيفية الاستخدام

### 1. التثبيت الأساسي

```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    
    <!-- الخطوط والمكتبات الخارجية -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    
    <!-- ملف CSS الموحد -->
    <link rel="stylesheet" href="mobile-unified.css">
</head>
<body class="mobile-device">
    <!-- محتوى الصفحة -->
    
    <!-- المكتبات الخارجية -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- ملف JavaScript الموحد -->
    <script src="mobile-unified.js"></script>
</body>
</html>
```

### 2. الهيكل الأساسي للواجهة

```html
<div class="mobile-view">
    <!-- رأس الصفحة -->
    <header class="mobile-header">
        <div class="logo">Loacker</div>
        <button class="menu-btn">☰</button>
    </header>
    
    <!-- محتوى الصفحة -->
    <main class="mobile-content">
        <!-- المحتوى هنا -->
    </main>
    
    <!-- تذييل الصفحة مع التبويبات -->
    <footer class="mobile-tabs">
        <button class="mobile-tab-btn active" data-tab="map">
            <i class="fas fa-map"></i>
            <span>الخريطة</span>
        </button>
        <!-- المزيد من التبويبات -->
    </footer>
</div>
```

### 3. إنشاء بطاقة متجر

```html
<div class="mobile-store-card">
    <div class="store-name">اسم المتجر</div>
    <div class="store-address">
        <i class="fas fa-map-marker-alt"></i> العنوان
    </div>
    <div class="store-phone">
        <a href="tel:+123456789">
            <i class="fas fa-phone"></i> +123456789
        </a>
    </div>
    <div class="mobile-share-buttons">
        <button class="mobile-share-btn mobile-share-whatsapp">
            <i class="fab fa-whatsapp"></i> واتساب
        </button>
        <button class="mobile-share-btn mobile-share-copy">
            <i class="fas fa-copy"></i> نسخ
        </button>
    </div>
</div>
```

### 4. استخدام JavaScript

```javascript
// التطبيق يتم تهيئته تلقائياً
// يمكن الوصول إليه عبر window.mobileApp

// مثال على استخدام الوظائف
mobileApp.shareStore(storeId, 'whatsapp');
mobileApp.showSuccess('تم بنجاح!');
mobileApp.loadStores();
```

## المميزات الرئيسية

### 1. كشف الأجهزة التلقائي
- كشف نوع الجهاز (iPhone, Android, etc.)
- كشف العلامة التجارية (Samsung, Xiaomi, etc.)
- كشف حجم الشاشة وتطبيق التنسيقات المناسبة
- دعم الاتجاه العمودي والأفقي

### 2. واجهة مستخدم متجاوبة
- تصميم متجاوب لجميع أحجام الشاشات
- دعم المناطق الآمنة للهواتف الحديثة
- تأثيرات بصرية سلسة
- تفاعل محسن للمس

### 3. إدارة البيانات
- تحميل المتاجر من API
- فلترة وبحث متقدم
- إدارة القوائم المخصصة
- تحديث الإحصائيات

### 4. الخرائط التفاعلية
- تكامل مع Leaflet
- عرض المتاجر على الخريطة
- تحديد الموقع الحالي
- أزرار تحكم مخصصة

### 5. المشاركة والتصدير
- مشاركة عبر واتساب
- نسخ إلى الحافظة
- دعم واجهة المشاركة الأصلية
- تصدير البيانات

## التخصيص والتطوير

### 1. تخصيص الألوان

```css
:root {
    --loacker-red: #d50000;        /* اللون الأساسي */
    --loacker-red-light: #ff5131; /* اللون الفاتح */
    --loacker-red-dark: #9b0000;  /* اللون الداكن */
}
```

### 2. إضافة مكونات جديدة

```css
.my-custom-component {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-sm);
    padding: 1rem;
    margin-bottom: 1rem;
}
```

### 3. إضافة وظائف جديدة

```javascript
// إضافة وظيفة جديدة للفئة
MobileApp.prototype.myCustomFunction = function() {
    // الكود هنا
};

// أو استخدام التطبيق الموجود
window.mobileApp.myCustomFunction = function() {
    // الكود هنا
};
```

## الأداء والتحسين

### 1. تحسينات CSS
- استخدام متغيرات CSS للقيم المتكررة
- تجميع الأنماط المتشابهة
- استخدام `will-change` للعناصر المتحركة
- تحسين الانتقالات والرسوم المتحركة

### 2. تحسينات JavaScript
- تأخير تحميل المكونات غير الضرورية
- استخدام `debounce` للبحث
- تحسين معالجة الأحداث
- إدارة الذاكرة بكفاءة

### 3. تحسينات الشبكة
- تحميل البيانات حسب الحاجة
- استخدام التخزين المؤقت
- ضغط الاستجابات
- تحسين الصور

## الاختبار والتطوير

### 1. اختبار الأجهزة
- اختبار على أجهزة iPhone مختلفة
- اختبار على أجهزة Android مختلفة
- اختبار الاتجاهات المختلفة
- اختبار أحجام الشاشات المختلفة

### 2. اختبار الوظائف
- اختبار التنقل بين التبويبات
- اختبار البحث والفلترة
- اختبار المشاركة
- اختبار الخرائط

### 3. اختبار الأداء
- قياس سرعة التحميل
- قياس استهلاك الذاكرة
- قياس سلاسة الرسوم المتحركة
- قياس استجابة اللمس

## الصيانة والتحديث

### 1. إضافة ميزات جديدة
1. إضافة CSS في القسم المناسب
2. إضافة JavaScript في الفئة المناسبة
3. تحديث القالب إذا لزم الأمر
4. اختبار على الأجهزة المختلفة

### 2. إصلاح الأخطاء
1. تحديد مصدر الخطأ
2. إصلاح الكود في الملف المناسب
3. اختبار الإصلاح
4. توثيق التغيير

### 3. تحسين الأداء
1. تحليل الأداء الحالي
2. تحديد نقاط الضعف
3. تطبيق التحسينات
4. قياس التحسن

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من ملف القالب للأمثلة
3. اختبر على أجهزة مختلفة
4. اتصل بفريق التطوير

---

**ملاحظة:** هذه الملفات تم تطويرها خصيصاً لتطبيق Loacker وتحتوي على جميع الوظائف المطلوبة للهواتف المحمولة. يمكن تخصيصها وتطويرها حسب الحاجة.
