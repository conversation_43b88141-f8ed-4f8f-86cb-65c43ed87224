/**
 * ملف CSS موحد ومنظم لواجهة الهاتف المحمول - Loacker
 * يجمع جميع التنسيقات والأنماط المطلوبة للهواتف المحمولة
 * تم إنشاؤه: 2024
 * 
 * المحتويات:
 * 1. المتغيرات الأساسية
 * 2. كشف الأجهزة والتوافق
 * 3. الهيكل الأساسي للواجهة
 * 4. أنماط الرأس والتذييل
 * 5. أنماط المحتوى والبطاقات
 * 6. أنماط الأزرار والتفاعل
 * 7. أنماط الخريطة والموقع
 * 8. أنماط النماذج والمدخلات
 * 9. أنماط الإشعارات والرسائل
 * 10. إصلاحات خاصة بالأجهزة
 */

/* ===== 1. المتغيرات الأساسية ===== */
:root {
    /* ألوان Loacker */
    --loacker-red: #d50000;
    --loacker-red-light: #ff5131;
    --loacker-red-dark: #9b0000;
    
    /* أبعاد الواجهة */
    --header-height: 60px;
    --footer-height: 60px;
    --content-padding: 15px;
    --card-border-radius: 12px;
    --button-border-radius: 8px;
    
    /* الظلال */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 6px 16px rgba(0, 0, 0, 0.2);
    
    /* الانتقالات */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* متغيرات الأجهزة */
    --device-width: 100vw;
    --device-height: 100vh;
    --device-pixel-ratio: 1;
    --device-type: "unknown";
    --device-model: "unknown";
    --safe-area-inset-top: 0px;
    --safe-area-inset-bottom: 0px;
    --font-size-multiplier: 1;
    --device-font-family: "Tajawal", "Cairo", sans-serif;
}

/* ===== 2. كشف الأجهزة والتوافق ===== */

/* أنماط عامة للأجهزة المحمولة */
body.mobile-device {
    font-family: var(--device-font-family);
    font-size: calc(14px * var(--font-size-multiplier));
    padding-top: var(--safe-area-inset-top);
    padding-bottom: var(--safe-area-inset-bottom);
    background-color: #f8f9fa;
    margin: 0;
    overflow-x: hidden;
}

/* أنماط للهواتف الصغيرة (مثل iPhone 7/8) */
body.mobile-device.small-phone {
    font-size: calc(13px * var(--font-size-multiplier));
}

/* أنماط للهواتف الكبيرة (مثل iPhone 11/12) */
body.mobile-device.large-phone {
    font-size: calc(15px * var(--font-size-multiplier));
}

/* أنماط خاصة بأجهزة iPhone */
body.mobile-device.iphone-device {
    --ios-blue: #007aff;
    --ios-red: #ff3b30;
    --ios-green: #34c759;
}

/* أنماط خاصة بأجهزة Xiaomi */
body.mobile-device.xiaomi-device {
    --miui-blue: #0e87fa;
    --miui-orange: #ff6700;
}

/* أنماط خاصة بأجهزة Samsung */
body.mobile-device.samsung-device {
    --samsung-blue: #1428a0;
    --samsung-light-blue: #75b7ff;
}

/* أنماط خاصة بالاتجاه العمودي */
body.portrait-mode .mobile-view {
    max-width: 100vw;
    height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
}

/* أنماط خاصة بالاتجاه الأفقي */
body.landscape-mode .mobile-view {
    max-width: 100vw;
    height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
}

/* ===== 3. الهيكل الأساسي للواجهة ===== */

/* واجهة الهاتف المحمول الرئيسية */
.mobile-view {
    display: none;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: #f8f9fa !important;
    color: #333 !important;
    font-family: 'Tajawal', sans-serif;
    height: 100vh;
    position: relative;
}

/* عرض واجهة الهاتف عند الحاجة */
body.mobile-device .mobile-view {
    display: flex;
    flex-direction: column;
}

/* إخفاء واجهة سطح المكتب على الهواتف */
body.mobile-device .desktop-view {
    display: none !important;
}

/* ===== 4. أنماط الرأس والتذييل ===== */

/* رأس الصفحة للهاتف المحمول */
.mobile-header {
    background-color: white !important;
    color: #333 !important;
    box-shadow: var(--shadow-sm) !important;
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--content-padding);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid #eee;
}

.mobile-header .logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--loacker-red);
}

.mobile-header .menu-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #333;
    padding: 0.5rem;
    border-radius: var(--button-border-radius);
    transition: var(--transition-fast);
}

.mobile-header .menu-btn:hover {
    background-color: #f5f5f5;
}

/* محتوى الصفحة */
.mobile-content {
    flex: 1;
    background-color: #f8f9fa !important;
    color: #333 !important;
    padding: var(--content-padding);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* تذييل الصفحة مع التبويبات */
.mobile-tabs {
    background-color: white !important;
    border-top: 1px solid #eee !important;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1) !important;
    height: var(--footer-height);
    display: flex;
    align-items: center;
    position: sticky;
    bottom: 0;
    z-index: 1000;
}

/* ===== 5. أنماط المحتوى والبطاقات ===== */

/* بطاقات المتاجر */
.mobile-store-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
    padding: 1rem;
    transition: var(--transition-normal);
    border: 1px solid #eee;
}

.mobile-store-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.mobile-store-card .store-name {
    font-size: 1.1rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.mobile-store-card .store-address {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.mobile-store-card .store-phone {
    color: var(--loacker-red);
    font-weight: 500;
    text-decoration: none;
}

/* قائمة المتاجر */
.mobile-stores-list {
    padding: 0;
    margin: 0;
}

.mobile-store-item {
    list-style: none;
    margin-bottom: 0.5rem;
}

/* ===== 6. أنماط الأزرار والتفاعل ===== */

/* أزرار التبويبات */
.mobile-tab-btn {
    background-color: white !important;
    color: #666 !important;
    border: none;
    border-radius: 0;
    padding: 0.75rem 0;
    font-size: 0.85rem;
    transition: var(--transition-normal);
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.mobile-tab-btn i {
    font-size: 1.25rem;
    transition: var(--transition-normal);
}

.mobile-tab-btn:active {
    background-color: #f5f5f5 !important;
    transform: scale(0.95);
}

.mobile-tab-btn.active {
    color: var(--loacker-red) !important;
}

.mobile-tab-btn.active i {
    transform: scale(1.1);
}

/* أزرار عامة */
.mobile-btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--button-border-radius);
    border: none;
    font-weight: 500;
    transition: var(--transition-normal);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    min-height: 44px; /* الحد الأدنى للمس */
}

.mobile-btn-primary {
    background-color: var(--loacker-red);
    color: white;
}

.mobile-btn-primary:hover {
    background-color: var(--loacker-red-dark);
}

.mobile-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.mobile-btn-outline {
    background-color: transparent;
    border: 2px solid var(--loacker-red);
    color: var(--loacker-red);
}

.mobile-btn-outline:hover {
    background-color: var(--loacker-red);
    color: white;
}

/* تأثير الضغط للأزرار */
.mobile-btn:active {
    transform: scale(0.95);
}

/* ===== 7. أنماط الخريطة والموقع ===== */

/* حاوي الخريطة */
.mobile-map-container {
    height: 300px;
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
}

.mobile-map {
    width: 100%;
    height: 100%;
}

/* أزرار التحكم في الخريطة */
.mobile-map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-map-control-btn {
    background: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.mobile-map-control-btn:hover {
    box-shadow: var(--shadow-md);
}

/* ===== 8. أنماط النماذج والمدخلات ===== */

/* حقول الإدخال */
.mobile-form-group {
    margin-bottom: 1rem;
}

.mobile-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.mobile-form-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: var(--button-border-radius);
    font-size: 1rem;
    transition: var(--transition-fast);
    background-color: white;
}

.mobile-form-input:focus {
    outline: none;
    border-color: var(--loacker-red);
    box-shadow: 0 0 0 3px rgba(213, 0, 0, 0.1);
}

.mobile-form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* قوائم الاختيار */
.mobile-form-select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

/* ===== 9. أنماط الإشعارات والرسائل ===== */

/* إشعارات عامة */
.mobile-notification {
    padding: 1rem;
    border-radius: var(--button-border-radius);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.mobile-notification-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.mobile-notification-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.mobile-notification-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.mobile-notification-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* رسائل التحميل */
.mobile-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #666;
}

.mobile-loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--loacker-red);
    border-radius: 50%;
    animation: mobile-spin 1s linear infinite;
    margin-right: 0.75rem;
}

@keyframes mobile-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 10. إصلاحات خاصة بالأجهزة ===== */

/* إصلاحات لأجهزة iPhone */
.mobile-view.iphone-view {
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", sans-serif;
}

.mobile-view.iphone-view button,
.mobile-view.iphone-view .mobile-btn {
    border-radius: 10px;
}

/* إصلاحات لأجهزة Xiaomi */
.mobile-view.xiaomi-view {
    font-family: "Mi Sans", "Roboto", sans-serif;
}

/* إصلاحات لأجهزة Samsung */
.mobile-view.samsung-view {
    font-family: "Samsung Sans", "Roboto", sans-serif;
}

/* إصلاحات للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mobile-view {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* إصلاحات للمناطق الآمنة في الهواتف الحديثة */
@supports (padding: max(0px)) {
    .mobile-view {
        padding-top: max(var(--safe-area-inset-top), 0px);
        padding-bottom: max(var(--safe-area-inset-bottom), 0px);
    }
}

/* إصلاحات للتمرير السلس */
.mobile-content {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* إصلاحات لمنع التكبير غير المرغوب فيه */
.mobile-form-input,
.mobile-form-select,
.mobile-form-textarea {
    font-size: 16px; /* منع التكبير التلقائي في iOS */
}

/* إصلاحات للمس والتفاعل */
.mobile-view * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

.mobile-form-input,
.mobile-form-textarea {
    -webkit-user-select: text;
    user-select: text;
}

/* إصلاحات للأداء */
.mobile-view {
    will-change: transform;
    transform: translateZ(0);
}

.mobile-store-card,
.mobile-btn,
.mobile-tab-btn {
    will-change: transform;
    transform: translateZ(0);
}

/* ===== 11. استعلامات الوسائط للاستجابة ===== */

/* شاشات صغيرة جداً (أقل من 320px) */
@media (max-width: 319px) {
    :root {
        --content-padding: 10px;
        --font-size-multiplier: 0.9;
    }

    .mobile-header {
        padding: 0 10px;
    }

    .mobile-tab-btn {
        font-size: 0.75rem;
        padding: 0.5rem 0;
    }

    .mobile-tab-btn i {
        font-size: 1rem;
    }
}

/* شاشات صغيرة (320px - 480px) */
@media (min-width: 320px) and (max-width: 480px) {
    :root {
        --content-padding: 12px;
        --font-size-multiplier: 1;
    }
}

/* شاشات متوسطة (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    :root {
        --content-padding: 20px;
        --font-size-multiplier: 1.1;
    }

    .mobile-store-card {
        padding: 1.25rem;
    }

    .mobile-map-container {
        height: 350px;
    }
}

/* الاتجاه الأفقي للهواتف */
@media (orientation: landscape) and (max-height: 500px) {
    .mobile-header {
        height: 50px;
    }

    .mobile-tabs {
        height: 50px;
    }

    .mobile-content {
        padding: 10px;
    }

    .mobile-map-container {
        height: 200px;
    }
}

/* ===== 12. أنماط خاصة بالمكونات المتقدمة ===== */

/* شريط البحث */
.mobile-search-bar {
    position: relative;
    margin-bottom: 1rem;
}

.mobile-search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #ddd;
    border-radius: 25px;
    font-size: 1rem;
    background-color: white;
    transition: var(--transition-fast);
}

.mobile-search-input:focus {
    outline: none;
    border-color: var(--loacker-red);
    box-shadow: 0 0 0 3px rgba(213, 0, 0, 0.1);
}

.mobile-search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 1.1rem;
}

/* فلاتر المتاجر */
.mobile-filters {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    -webkit-overflow-scrolling: touch;
}

.mobile-filter-btn {
    background-color: white;
    border: 2px solid #ddd;
    color: #666;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    white-space: nowrap;
    transition: var(--transition-fast);
    cursor: pointer;
}

.mobile-filter-btn.active {
    background-color: var(--loacker-red);
    border-color: var(--loacker-red);
    color: white;
}

.mobile-filter-btn:hover {
    border-color: var(--loacker-red);
}

/* عدادات الإحصائيات */
.mobile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.mobile-stat-card {
    background: white;
    padding: 1rem;
    border-radius: var(--card-border-radius);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid #eee;
}

.mobile-stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--loacker-red);
    display: block;
}

.mobile-stat-label {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.25rem;
}

/* قائمة منسدلة للمناطق */
.mobile-region-selector {
    margin-bottom: 1rem;
}

.mobile-region-btn {
    width: 100%;
    background: white;
    border: 2px solid #ddd;
    padding: 0.75rem 1rem;
    border-radius: var(--button-border-radius);
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: var(--transition-fast);
}

.mobile-region-btn:hover {
    border-color: var(--loacker-red);
}

.mobile-region-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #ddd;
    border-top: none;
    border-radius: 0 0 var(--button-border-radius) var(--button-border-radius);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.mobile-region-dropdown.show {
    display: block;
}

.mobile-region-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition-fast);
    border-bottom: 1px solid #eee;
}

.mobile-region-option:hover {
    background-color: #f8f9fa;
}

.mobile-region-option.selected {
    background-color: var(--loacker-red);
    color: white;
}

/* نافذة منبثقة للمتجر */
.mobile-store-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: var(--card-border-radius) var(--card-border-radius) 0 0;
    box-shadow: var(--shadow-lg);
    transform: translateY(100%);
    transition: transform var(--transition-normal);
    z-index: 2000;
    max-height: 70vh;
    overflow-y: auto;
}

.mobile-store-popup.show {
    transform: translateY(0);
}

.mobile-store-popup-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-store-popup-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.mobile-store-popup-close:hover {
    background-color: #f5f5f5;
}

.mobile-store-popup-content {
    padding: 1rem;
}

/* أزرار المشاركة */
.mobile-share-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.mobile-share-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: var(--button-border-radius);
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.mobile-share-whatsapp {
    background-color: #25d366;
}

.mobile-share-whatsapp:hover {
    background-color: #1da851;
}

.mobile-share-copy {
    background-color: #6c757d;
}

.mobile-share-copy:hover {
    background-color: #545b62;
}

/* رسائل التأكيد */
.mobile-toast {
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 0.9rem;
    z-index: 3000;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.mobile-toast.show {
    opacity: 1;
}

/* ===== 13. أنماط الرسوم المتحركة ===== */

/* تأثير الموجة عند الضغط */
@keyframes mobile-ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

.mobile-btn:after,
.mobile-tab-btn:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.mobile-btn:focus:not(:active):after,
.mobile-tab-btn:focus:not(:active):after {
    animation: mobile-ripple 1s ease-out;
}

/* تأثير الظهور التدريجي */
@keyframes mobile-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-store-card {
    animation: mobile-fade-in 0.3s ease-out;
}

/* تأثير الانزلاق من الأسفل */
@keyframes mobile-slide-up {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.mobile-store-popup.show {
    animation: mobile-slide-up var(--transition-normal) ease-out;
}

/* ===== 14. أنماط الطباعة (اختيارية) ===== */

@media print {
    .mobile-view {
        display: block !important;
        height: auto !important;
        overflow: visible !important;
    }

    .mobile-header,
    .mobile-tabs,
    .mobile-btn,
    .mobile-map-container {
        display: none !important;
    }

    .mobile-content {
        padding: 0 !important;
    }

    .mobile-store-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
