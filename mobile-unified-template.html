<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#d50000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Loacker">
    
    <title>Loacker - دليل المتاجر</title>
    
    <!-- الخطوط العربية -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Leaflet للخرائط -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    
    <!-- ملف CSS الموحد للهاتف المحمول -->
    <link rel="stylesheet" href="mobile-unified.css">
    
    <!-- إعدادات إضافية للهواتف -->
    <style>
        /* إعدادات أساسية للصفحة */
        * {
            box-sizing: border-box;
        }
        
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow-x: hidden;
        }
        
        /* إخفاء واجهة سطح المكتب افتراضياً */
        .desktop-view {
            display: block;
        }
        
        .mobile-view {
            display: none;
        }
        
        /* عرض واجهة الهاتف على الأجهزة المحمولة */
        @media (max-width: 768px) {
            .desktop-view {
                display: none !important;
            }
            
            .mobile-view {
                display: flex !important;
            }
            
            body {
                font-family: 'Tajawal', sans-serif;
            }
        }
    </style>
</head>
<body class="mobile-device">
    
    <!-- واجهة سطح المكتب -->
    <div class="desktop-view">
        <div style="padding: 2rem; text-align: center;">
            <h1>مرحباً بك في Loacker</h1>
            <p>يرجى استخدام هاتفك المحمول للوصول إلى التطبيق</p>
        </div>
    </div>
    
    <!-- واجهة الهاتف المحمول -->
    <div class="mobile-view">
        
        <!-- رأس الصفحة -->
        <header class="mobile-header">
            <div class="logo">
                <i class="fas fa-store"></i>
                Loacker
            </div>
            <button class="menu-btn" id="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </header>
        
        <!-- محتوى الصفحة -->
        <main class="mobile-content">
            
            <!-- شريط البحث -->
            <div id="mobile-search-container"></div>
            
            <!-- الفلاتر -->
            <div id="mobile-filters-container"></div>
            
            <!-- محتوى التبويبات -->
            <div class="mobile-tabs-content">
                
                <!-- تبويب الخريطة -->
                <div id="mobile-map-tab" class="mobile-tab-content">
                    <div class="mobile-stats">
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-total-stores-count">0</span>
                            <span class="mobile-stat-label">إجمالي المتاجر</span>
                        </div>
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-regions-count">0</span>
                            <span class="mobile-stat-label">المناطق</span>
                        </div>
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-avg-stores-per-region">0</span>
                            <span class="mobile-stat-label">متوسط/منطقة</span>
                        </div>
                    </div>
                    
                    <div class="mobile-map-container">
                        <div id="mobile-map" class="mobile-map"></div>
                    </div>
                </div>
                
                <!-- تبويب القائمة -->
                <div id="mobile-list-tab" class="mobile-tab-content" style="display: none;">
                    <div id="mobile-lists-container" class="mobile-filters"></div>
                    <div id="mobile-stores-container" class="mobile-stores-list"></div>
                </div>
                
                <!-- تبويب الإحصائيات -->
                <div id="mobile-stats-tab" class="mobile-tab-content" style="display: none;">
                    <div class="mobile-stats">
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-total-stores-count-2">0</span>
                            <span class="mobile-stat-label">إجمالي المتاجر</span>
                        </div>
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-regions-count-2">0</span>
                            <span class="mobile-stat-label">عدد المناطق</span>
                        </div>
                        <div class="mobile-stat-card">
                            <span class="mobile-stat-number" id="mobile-avg-stores-per-region-2">0</span>
                            <span class="mobile-stat-label">متوسط المتاجر لكل منطقة</span>
                        </div>
                    </div>
                    
                    <div class="mobile-store-card">
                        <h6>إحصائيات مفصلة</h6>
                        <p>ستتوفر الإحصائيات المفصلة قريباً...</p>
                    </div>
                </div>
                
                <!-- تبويب الإعدادات -->
                <div id="mobile-settings-tab" class="mobile-tab-content" style="display: none;">
                    <div class="mobile-store-card">
                        <h6><i class="fas fa-cog"></i> الإعدادات</h6>
                        
                        <div class="mobile-form-group">
                            <label class="mobile-form-label">اللغة</label>
                            <select class="mobile-form-input mobile-form-select">
                                <option value="ar">العربية</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                        
                        <div class="mobile-form-group">
                            <label class="mobile-form-label">المظهر</label>
                            <select class="mobile-form-input mobile-form-select">
                                <option value="light">فاتح</option>
                                <option value="dark">داكن</option>
                                <option value="auto">تلقائي</option>
                            </select>
                        </div>
                        
                        <div class="mobile-form-group">
                            <button class="mobile-btn mobile-btn-primary" style="width: 100%;">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                    
                    <div class="mobile-store-card">
                        <h6><i class="fas fa-info-circle"></i> حول التطبيق</h6>
                        <p>تطبيق Loacker لدليل المتاجر</p>
                        <p>الإصدار: 1.0.0</p>
                        <p>تم التطوير بواسطة فريق Loacker</p>
                    </div>
                </div>
                
            </div>
            
        </main>
        
        <!-- تذييل الصفحة مع التبويبات -->
        <footer class="mobile-tabs">
            <button class="mobile-tab-btn active" data-tab="map">
                <i class="fas fa-map"></i>
                <span>الخريطة</span>
            </button>
            <button class="mobile-tab-btn" data-tab="list">
                <i class="fas fa-list"></i>
                <span>القائمة</span>
            </button>
            <button class="mobile-tab-btn" data-tab="stats">
                <i class="fas fa-chart-bar"></i>
                <span>الإحصائيات</span>
            </button>
            <button class="mobile-tab-btn" data-tab="settings">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </button>
        </footer>
        
    </div>
    
    <!-- نافذة منبثقة لتفاصيل المتجر -->
    <div class="mobile-store-popup" id="mobile-store-popup">
        <div class="mobile-store-popup-header">
            <h6>تفاصيل المتجر</h6>
            <button class="mobile-store-popup-close" onclick="document.getElementById('mobile-store-popup').classList.remove('show')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mobile-store-popup-content" id="mobile-store-popup-content">
            <!-- سيتم ملء المحتوى ديناميكياً -->
        </div>
    </div>
    
    <!-- مكتبات JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- ملف JavaScript الموحد للهاتف المحمول -->
    <script src="mobile-unified.js"></script>
    
    <!-- سكريبت إضافي لكشف الجهاز -->
    <script>
        // كشف نوع الجهاز وتطبيق الفئات المناسبة
        (function() {
            const userAgent = navigator.userAgent;
            const body = document.body;
            
            // كشف نوع الجهاز
            if (/iPhone/i.test(userAgent)) {
                body.classList.add('iphone-device');
            } else if (/iPad/i.test(userAgent)) {
                body.classList.add('ipad-device');
            } else if (/Android/i.test(userAgent)) {
                body.classList.add('android-device');
                
                // كشف العلامة التجارية للأندرويد
                if (/Xiaomi|Mi |Redmi/i.test(userAgent)) {
                    body.classList.add('xiaomi-device');
                } else if (/Samsung|SM-/i.test(userAgent)) {
                    body.classList.add('samsung-device');
                } else if (/Huawei|Honor/i.test(userAgent)) {
                    body.classList.add('huawei-device');
                }
            }
            
            // كشف حجم الشاشة
            const screenWidth = window.screen.width;
            if (screenWidth <= 375) {
                body.classList.add('small-phone');
            } else if (screenWidth >= 414) {
                body.classList.add('large-phone');
            }
            
            // كشف الاتجاه
            function updateOrientation() {
                const isPortrait = window.innerHeight > window.innerWidth;
                body.classList.toggle('portrait-mode', isPortrait);
                body.classList.toggle('landscape-mode', !isPortrait);
            }
            
            updateOrientation();
            window.addEventListener('orientationchange', () => {
                setTimeout(updateOrientation, 100);
            });
            window.addEventListener('resize', updateOrientation);
            
            // تعيين متغيرات CSS للجهاز
            document.documentElement.style.setProperty('--device-width', window.screen.width + 'px');
            document.documentElement.style.setProperty('--device-height', window.screen.height + 'px');
            document.documentElement.style.setProperty('--device-pixel-ratio', window.devicePixelRatio || 1);
            
            // تعيين المناطق الآمنة للهواتف الحديثة
            if (CSS.supports('padding: env(safe-area-inset-top)')) {
                document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)');
                document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)');
            }
        })();
        
        // منع التكبير عند النقر المزدوج
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // منع التمرير المطاطي في iOS
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('.mobile-content') || e.target.closest('.mobile-map')) {
                return;
            }
            e.preventDefault();
        }, { passive: false });
    </script>
    
</body>
</html>
